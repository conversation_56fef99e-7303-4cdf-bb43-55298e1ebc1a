
// Sample data - In real application, this would come from backend API
let users = [];
let gameLevels = [];
let currentPage = 1;
let usersPerPage = 50;
let filteredUsers = [];

// Initialize sample data
function initializeData() {
    // Generate sample users
    for (let i = 1; i <= 150; i++) {
        users.push({
            id: i,
            username: `user${i}`,
            email: `user${i}@example.com`,
            password: '********', // In real app, never show actual passwords
            achievements: Math.floor(Math.random() * 50) + 1,
            totalAchievements: 50,
            levelsCompleted: Math.floor(Math.random() * 20) + 1,
            totalLevels: 20,
            tier: getTierFromExp(Math.floor(Math.random() * 10000)),
            exp: Math.floor(Math.random() * 10000),
            joinDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
        });
    }

    // Initialize game levels with sample questions
    gameLevels = [
        {
            levelID: 1,
            levelName: "Basic Networking",
            isActive: true,
            questions: [
                {
                    id: 1,
                    question: "What is the capital of France?",
                    options: ["London", "Berlin", "Paris", "Madrid"],
                    correctAnswer: "C",
                    difficulty: "Easy"
                },
                {
                    id: 2,
                    question: "Which programming language is known as the 'language of the web'?",
                    options: ["Python", "JavaScript", "Java", "C++"],
                    correctAnswer: "B",
                    difficulty: "Medium"
                }
            ]
        },
        {
            levelID: 2,
            levelName: "Network Protocols",
            isActive: true,
            questions: [
                {
                    id: 3,
                    question: "What is the time complexity of binary search?",
                    options: ["O(n)", "O(log n)", "O(n²)", "O(1)"],
                    correctAnswer: "B",
                    difficulty: "Hard"
                },
                {
                    id: 4,
                    question: "Which device connects multiple computers in a LAN?",
                    options: ["Router", "Switch", "Hub", "Modem"],
                    correctAnswer: "B",
                    difficulty: "Medium"
                }
            ]
        },
        {
            levelID: 3,
            levelName: "Network Security",
            isActive: true,
            questions: [
                {
                    id: 5,
                    question: "What does VPN stand for?",
                    options: ["Virtual Private Network", "Very Private Network", "Virtual Public Network", "Very Public Network"],
                    correctAnswer: "A",
                    difficulty: "Easy"
                }
            ]
        },
        {
            levelID: 4,
            levelName: "Advanced Networking",
            isActive: false,
            questions: []
        },
        {
            levelID: 5,
            levelName: "Network Troubleshooting",
            isActive: false,
            questions: []
        },
        {
            levelID: 6,
            levelName: "Network Management",
            isActive: false,
            questions: []
        }
    ];

    filteredUsers = [...users];
}

function getTierFromExp(exp) {
    if (exp < 1000) return "Bronze";
    if (exp < 3000) return "Silver";
    if (exp < 6000) return "Gold";
    if (exp < 10000) return "Platinum";
    return "Diamond";
}

// Navigation functions
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Show selected section
    document.getElementById(`${sectionName}-section`).classList.add('active');

    // Add active class to clicked nav item
    event.target.closest('.nav-item').classList.add('active');

    if (sectionName === 'users') {
        displayUsers();
    } else if (sectionName === 'content') {
        displayGameLevels();
    }
}

// Users management functions
function displayUsers() {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const usersToShow = filteredUsers.slice(startIndex, endIndex);

    const usersGrid = document.getElementById('usersGrid');
    usersGrid.innerHTML = '';

    usersToShow.forEach(user => {
        const userCard = createUserCard(user);
        usersGrid.appendChild(userCard);
    });

    updatePagination();
}

function createUserCard(user) {
    const card = document.createElement('div');
    card.className = 'user-card';
    card.onclick = () => showUserDetails(user);

    card.innerHTML = `
        <div class="user-card-header">
            <div class="user-info">
                <h3>${user.username}</h3>
                <p>${user.email}</p>
            </div>
            <div class="user-actions">
                <button class="three-dots" onclick="toggleDropdown(event, ${user.id})">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu" id="dropdown-${user.id}">
                    <a href="#" class="dropdown-item" onclick="showUserDetails(${JSON.stringify(user).replace(/"/g, '&quot;')})">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <a href="#" class="dropdown-item" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </div>
            </div>
        </div>
        <div class="user-stats">
            <div class="stat">
                <div class="stat-value">${user.achievements}</div>
                <div class="stat-label">Achievements</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.levelsCompleted}</div>
                <div class="stat-label">Levels</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.tier}</div>
                <div class="stat-label">Tier</div>
            </div>
        </div>
    `;

    return card;
}

function toggleDropdown(event, userId) {
    event.stopPropagation();

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== `dropdown-${userId}`) {
            menu.classList.remove('show');
        }
    });

    // Toggle current dropdown
    const dropdown = document.getElementById(`dropdown-${userId}`);
    dropdown.classList.toggle('show');
}

function showUserDetails(user) {
    const modal = document.getElementById('userModal');
    const modalBody = document.getElementById('userModalBody');

    const achievementProgress = (user.achievements / user.totalAchievements) * 100;
    const levelProgress = (user.levelsCompleted / user.totalLevels) * 100;

    modalBody.innerHTML = `
        <div class="user-detail-section">
            <h3>Basic Information</h3>
            <div class="user-basic-info">
                <div class="info-item">
                    <div class="info-label">User ID</div>
                    <div class="info-value">${user.id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Username</div>
                    <div class="info-value">${user.username}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email</div>
                    <div class="info-value">${user.email}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Password</div>
                    <div class="info-value">${user.password}</div>
                </div>
            </div>
        </div>

        <div class="user-detail-section">
            <h3>Progress & Achievements</h3>
            <div class="progress-section">
                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Achievements Unlocked</span>
                        <span class="progress-value">${user.achievements}/${user.totalAchievements}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${achievementProgress}%"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Levels Completed</span>
                        <span class="progress-value">${user.levelsCompleted}/${user.totalLevels}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${levelProgress}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-detail-section">
            <h3>User Tier</h3>
            <div class="tier-display">
                <div class="tier-title">${user.tier} Tier</div>
                <div class="tier-exp">${user.exp} EXP</div>
            </div>
        </div>
    `;

    modal.classList.add('show');
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        users = users.filter(user => user.id !== userId);
        filteredUsers = filteredUsers.filter(user => user.id !== userId);
        displayUsers();

        // Close dropdown
        document.getElementById(`dropdown-${userId}`).classList.remove('show');
    }
}

function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    filteredUsers = users.filter(user =>
        user.username.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
    );
    currentPage = 1;
    displayUsers();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayUsers();
    }
}

function updatePagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prevBtn').disabled = currentPage === 1;
    document.getElementById('nextBtn').disabled = currentPage === totalPages;
}

// Game Levels management functions
function displayGameLevels() {
    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    gameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });
}

function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        const optionLabels = ['A', 'B', 'C', 'D'];
        const optionsHtml = question.options.map((option, index) => {
            const isCorrect = optionLabels[index] === question.correctAnswer;
            return `
                <li>
                    <span class="option-label">${optionLabels[index]}.</span>
                    ${option}
                    ${isCorrect ? '<span class="correct-answer">Correct</span>' : ''}
                </li>
            `;
        }).join('');

        return `
            <div class="question-item">
                <div class="question-header">
                    <div class="question-text">${question.question}</div>
                    <div class="question-actions">
                        <button class="btn-primary btn-small" onclick="editQuestion(${question.id}, ${level.levelID})">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn-danger btn-small" onclick="deleteQuestion(${question.id}, ${level.levelID})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                <div class="question-meta">
                    <span>Question ID: ${question.id}</span>
                    <span class="difficulty-badge difficulty-${question.difficulty.toLowerCase()}">${question.difficulty}</span>
                </div>
                <ul class="options-list">
                    ${optionsHtml}
                </ul>
            </div>
        `;
    }).join('');

    card.innerHTML = `
        <div class="level-header" onclick="toggleLevelDropdown(${level.levelID})">
            <div class="level-info">
                <div class="level-title-row">
                    <h3>Level ${level.levelID}: ${level.levelName}</h3>
                    <button class="dropdown-toggle" id="dropdown-toggle-${level.levelID}">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="level-meta">
                    <span class="level-status ${level.isActive ? 'active' : 'inactive'}">
                        ${level.isActive ? 'Active' : 'Inactive'}
                    </span>
                    <span class="questions-count">${level.questions.length} Questions</span>
                </div>
            </div>
            <div class="level-actions" onclick="event.stopPropagation()">
                <button class="btn-primary" onclick="showAddQuestionModal(${level.levelID})">
                    <i class="fas fa-plus"></i> Add Question
                </button>
                <!--<button class="btn-secondary" onclick="editLevel(${level.levelID})">
                    <i class="fas fa-edit"></i> Edit Level
                </button>
                <button class="btn-toggle ${level.isActive ? 'btn-warning' : 'btn-success'}" onclick="toggleLevel(${level.levelID})">
                    <i class="fas fa-power-off"></i> ${level.isActive ? 'Deactivate' : 'Activate'}
                </button>
                -->
            </div>
        </div>
        <div class="level-questions collapsed ${level.questions.length === 0 ? 'empty' : ''}" id="level-questions-${level.levelID}">
            ${level.questions.length === 0 ?
                '<div class="empty-questions">No questions added yet. Click "Add Question" to get started.</div>' :
                questionsHtml
            }
        </div>
    `;

    return card;
}

function showAddQuestionModal(levelID) {
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('questionForm').reset();
    document.getElementById('questionForm').onsubmit = (e) => handleAddQuestion(e, levelID);
    document.getElementById('questionModal').classList.add('show');
}

function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const question = level.questions.find(q => q.id === questionId);
    if (!question) return;

    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('questionText').value = question.question;
    document.getElementById('option1').value = question.options[0];
    document.getElementById('option2').value = question.options[1];
    document.getElementById('option3').value = question.options[2];
    document.getElementById('option4').value = question.options[3];
    document.getElementById('correctAnswer').value = question.correctAnswer;
    document.getElementById('difficulty').value = question.difficulty;

    document.getElementById('questionForm').onsubmit = (e) => handleEditQuestion(e, questionId, levelID);
    document.getElementById('questionModal').classList.add('show');
}

function handleAddQuestion(event, levelID) {
    event.preventDefault();

    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    // Get the highest question ID across all levels
    let maxId = 0;
    gameLevels.forEach(level => {
        level.questions.forEach(q => {
            if (q.id > maxId) maxId = q.id;
        });
    });

    const newQuestion = {
        id: maxId + 1,
        question: document.getElementById('questionText').value,
        options: [
            document.getElementById('option1').value,
            document.getElementById('option2').value,
            document.getElementById('option3').value,
            document.getElementById('option4').value
        ],
        correctAnswer: document.getElementById('correctAnswer').value,
        difficulty: document.getElementById('difficulty').value
    };

    level.questions.push(newQuestion);
    displayGameLevels();
    closeModal('questionModal');
}

function handleEditQuestion(event, questionId, levelID) {
    event.preventDefault();

    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const questionIndex = level.questions.findIndex(q => q.id === questionId);
    if (questionIndex === -1) return;

    level.questions[questionIndex] = {
        ...level.questions[questionIndex],
        question: document.getElementById('questionText').value,
        options: [
            document.getElementById('option1').value,
            document.getElementById('option2').value,
            document.getElementById('option3').value,
            document.getElementById('option4').value
        ],
        correctAnswer: document.getElementById('correctAnswer').value,
        difficulty: document.getElementById('difficulty').value
    };

    displayGameLevels();
    closeModal('questionModal');
}

function deleteQuestion(questionId, levelID) {
    if (confirm('Are you sure you want to delete this question?')) {
        const level = gameLevels.find(l => l.levelID === levelID);
        if (level) {
            level.questions = level.questions.filter(q => q.id !== questionId);
            displayGameLevels();
        }
    }
}

// Level management functions
function toggleLevelDropdown(levelID) {
    const questionsContainer = document.getElementById(`level-questions-${levelID}`);
    const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

    if (!questionsContainer || !dropdownToggle) return;

    const isCollapsed = questionsContainer.classList.contains('collapsed');

    if (isCollapsed) {
        // Expand
        questionsContainer.classList.remove('collapsed');
        questionsContainer.classList.add('expanded');
        dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
    } else {
        // Collapse
        questionsContainer.classList.remove('expanded');
        questionsContainer.classList.add('collapsed');
        dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
    }
}

function editLevel(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const newName = prompt('Enter new level name:', level.levelName);
    if (newName && newName.trim() !== '') {
        level.levelName = newName.trim();
        displayGameLevels();
    }
}

function toggleLevel(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const action = level.isActive ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} this level?`)) {
        level.isActive = !level.isActive;
        displayGameLevels();
    }
}

function toggleAllLevels() {
    const toggleButton = document.getElementById('toggleAllLevels');
    const isExpanding = toggleButton.textContent.includes('Expand');

    gameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${level.levelID}`);

        if (questionsContainer && dropdownToggle) {
            if (isExpanding) {
                // Expand all
                questionsContainer.classList.remove('collapsed');
                questionsContainer.classList.add('expanded');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
            } else {
                // Collapse all
                questionsContainer.classList.remove('expanded');
                questionsContainer.classList.add('collapsed');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
            }
        }
    });

    // Update button text and icon
    if (isExpanding) {
        toggleButton.innerHTML = '<i class="fas fa-compress-alt"></i> Collapse All';
    } else {
        toggleButton.innerHTML = '<i class="fas fa-expand-alt"></i> Expand All';
    }
}

// Modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeData();
    displayUsers();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.user-actions')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Close modals when clicking outside
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.remove('show');
            }
        });
    });
});

