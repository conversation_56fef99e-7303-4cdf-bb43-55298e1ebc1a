
// Global variables
let users = [];
let gameLevels = [];
let currentPage = 1;
let usersPerPage = 50;
let filteredUsers = [];
let gameContent = [];

// API Configuration
const API_BASE_URL = '../php/admin/';

// Initialize data from database
async function initializeData() {
    try {
        await Promise.all([
            loadUsers(),
            loadGameContent()
        ]);
    } catch (error) {
        console.error('Error initializing data:', error);
        showNotification('Error loading data from database', 'error');
    }
}

// API Functions
async function loadUsers() {
    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
        const result = await response.json();

        if (result.success) {
            users = result.data.map(user => ({
                id: user.user_id,
                username: user.username,
                email: user.email || 'N/A',
                name: user.name || 'N/A',
                password: '********', // Never show actual passwords
                achievements: user.achievements || 0,
                totalAchievements: user.totalAchievements || 50,
                levelsCompleted: user.levels_completed || 0,
                totalLevels: user.total_levels || 6,
                tier: user.tier || 'Bronze',
                exp: user.userExp || 0,
                joinDate: new Date(user.created_at)
            }));
            filteredUsers = [...users];
        } else {
            throw new Error(result.error || 'Failed to load users');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        // Fallback to empty array
        users = [];
        filteredUsers = [];
    }
}

async function loadGameContent() {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
        const result = await response.json();

        if (result.success) {
            gameContent = result.data;
            processGameLevels();
        } else {
            throw new Error(result.error || 'Failed to load game content');
        }
    } catch (error) {
        console.error('Error loading game content:', error);
        gameContent = [];
        gameLevels = [];
    }
}

function processGameLevels() {
    // Group content by level
    const levelGroups = {};

    gameContent.forEach(content => {
        const levelNum = content.level_number;
        if (!levelGroups[levelNum]) {
            levelGroups[levelNum] = {
                levelID: levelNum,
                levelName: `Level ${levelNum}`,
                isActive: true,
                questions: []
            };
        }

        levelGroups[levelNum].questions.push({
            id: content.content_id,
            question: content.question_text,
            options: [content.option1, content.option2, content.option3, content.option4],
            correctAnswer: content.correct_answer,
            difficulty: 'Medium' // Default difficulty since it's not in the database
        });
    });

    gameLevels = Object.values(levelGroups).sort((a, b) => a.levelID - b.levelID);

    // Add empty levels up to 6 if they don't exist
    for (let i = 1; i <= 6; i++) {
        if (!gameLevels.find(level => level.levelID === i)) {
            gameLevels.push({
                levelID: i,
                levelName: `Level ${i}`,
                isActive: false,
                questions: []
            });
        }
    }

    gameLevels.sort((a, b) => a.levelID - b.levelID);
}

function getTierFromExp(exp) {
    if (exp < 1000) return "Bronze";
    if (exp < 3000) return "Silver";
    if (exp < 6000) return "Gold";
    if (exp < 10000) return "Platinum";
    return "Diamond";
}

// Navigation functions
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Show selected section
    document.getElementById(`${sectionName}-section`).classList.add('active');

    // Add active class to clicked nav item
    event.target.closest('.nav-item').classList.add('active');

    if (sectionName === 'users') {
        displayUsers();
    } else if (sectionName === 'content') {
        displayGameLevels();
    }
}

// Users management functions
function displayUsers() {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const usersToShow = filteredUsers.slice(startIndex, endIndex);

    const usersGrid = document.getElementById('usersGrid');
    usersGrid.innerHTML = '';

    usersToShow.forEach(user => {
        const userCard = createUserCard(user);
        usersGrid.appendChild(userCard);
    });

    updatePagination();
}

function createUserCard(user) {
    const card = document.createElement('div');
    card.className = 'user-card';
    card.onclick = () => showUserDetails(user);

    card.innerHTML = `
        <div class="user-card-header">
            <div class="user-info">
                <h3>${user.username}</h3>
                <p>${user.email}</p>
            </div>
            <div class="user-actions">
                <button class="three-dots" onclick="toggleDropdown(event, ${user.id})">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu" id="dropdown-${user.id}">
                    <a href="#" class="dropdown-item" onclick="showUserDetails(${JSON.stringify(user).replace(/"/g, '&quot;')})">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <a href="#" class="dropdown-item" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </div>
            </div>
        </div>
        <div class="user-stats">
            <div class="stat">
                <div class="stat-value">${user.achievements}</div>
                <div class="stat-label">Achievements</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.levelsCompleted}</div>
                <div class="stat-label">Levels</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.tier}</div>
                <div class="stat-label">Tier</div>
            </div>
        </div>
    `;

    return card;
}

function toggleDropdown(event, userId) {
    event.stopPropagation();

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== `dropdown-${userId}`) {
            menu.classList.remove('show');
        }
    });

    // Toggle current dropdown
    const dropdown = document.getElementById(`dropdown-${userId}`);
    dropdown.classList.toggle('show');
}

function showUserDetails(user) {
    const modal = document.getElementById('userModal');
    const modalBody = document.getElementById('userModalBody');

    const achievementProgress = (user.achievements / user.totalAchievements) * 100;
    const levelProgress = (user.levelsCompleted / user.totalLevels) * 100;

    modalBody.innerHTML = `
        <div class="user-detail-section">
            <h3>Basic Information</h3>
            <div class="user-basic-info">
                <div class="info-item">
                    <div class="info-label">User ID</div>
                    <div class="info-value">${user.id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Username</div>
                    <div class="info-value">${user.username}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email</div>
                    <div class="info-value">${user.email}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Password</div>
                    <div class="info-value">${user.password}</div>
                </div>
            </div>
        </div>

        <div class="user-detail-section">
            <h3>Progress & Achievements</h3>
            <div class="progress-section">
                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Achievements Unlocked</span>
                        <span class="progress-value">${user.achievements}/${user.totalAchievements}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${achievementProgress}%"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Levels Completed</span>
                        <span class="progress-value">${user.levelsCompleted}/${user.totalLevels}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${levelProgress}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-detail-section">
            <h3>User Tier</h3>
            <div class="tier-display">
                <div class="tier-title">${user.tier} Tier</div>
                <div class="tier-exp">${user.exp} EXP</div>
            </div>
        </div>
    `;

    modal.classList.add('show');
}

async function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=delete_user&user_id=${userId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                users = users.filter(user => user.id !== userId);
                filteredUsers = filteredUsers.filter(user => user.id !== userId);
                displayUsers();
                showNotification('User deleted successfully', 'success');
            } else {
                showNotification(result.error || 'Failed to delete user', 'error');
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            showNotification('Error deleting user', 'error');
        }

        // Close dropdown
        const dropdown = document.getElementById(`dropdown-${userId}`);
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }
}

async function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredUsers = [...users];
    } else {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=search_users&search=${encodeURIComponent(searchTerm)}`);
            const result = await response.json();

            if (result.success) {
                filteredUsers = result.data.map(user => ({
                    id: user.user_id,
                    username: user.username,
                    email: user.email || 'N/A',
                    name: user.name || 'N/A',
                    password: '********',
                    achievements: user.achievements || 0,
                    totalAchievements: user.totalAchievements || 50,
                    levelsCompleted: user.levels_completed || 0,
                    totalLevels: user.total_levels || 6,
                    tier: user.tier || 'Bronze',
                    exp: user.userExp || 0,
                    joinDate: new Date(user.created_at)
                }));
            } else {
                filteredUsers = users.filter(user =>
                    user.username.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm)
                );
            }
        } catch (error) {
            console.error('Error searching users:', error);
            // Fallback to local search
            filteredUsers = users.filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm)
            );
        }
    }

    currentPage = 1;
    displayUsers();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayUsers();
    }
}

function updatePagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prevBtn').disabled = currentPage === 1;
    document.getElementById('nextBtn').disabled = currentPage === totalPages;
}

// Game Levels management functions
function displayGameLevels() {
    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    gameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });
}

function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        const optionLabels = ['A', 'B', 'C', 'D'];
        const optionsHtml = question.options.map((option, index) => {
            // Handle both letter-based (A,B,C,D) and number-based (1,2,3,4) correct answers
            const correctIndex = typeof question.correctAnswer === 'string'
                ? optionLabels.indexOf(question.correctAnswer.toUpperCase())
                : question.correctAnswer - 1;
            const isCorrect = index === correctIndex;
            return `
                <li>
                    <span class="option-label">${optionLabels[index]}.</span>
                    ${option}
                    ${isCorrect ? '<span class="correct-answer">Correct</span>' : ''}
                </li>
            `;
        }).join('');

        return `
            <div class="question-item">
                <div class="question-header">
                    <div class="question-text">${question.question}</div>
                    <div class="question-actions">
                        <button class="btn-primary btn-small" onclick="editQuestion(${question.id}, ${level.levelID})">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn-danger btn-small" onclick="deleteQuestion(${question.id}, ${level.levelID})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                <div class="question-meta">
                    <span>Question ID: ${question.id}</span>
                    <span class="difficulty-badge difficulty-${question.difficulty.toLowerCase()}">${question.difficulty}</span>
                </div>
                <ul class="options-list">
                    ${optionsHtml}
                </ul>
            </div>
        `;
    }).join('');

    card.innerHTML = `
        <div class="level-header" onclick="toggleLevelDropdown(${level.levelID})">
            <div class="level-info">
                <div class="level-title-row">
                    <h3>Level ${level.levelID}: ${level.levelName}</h3>
                    <button class="dropdown-toggle" id="dropdown-toggle-${level.levelID}">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="level-meta">
                    <span class="level-status ${level.isActive ? 'active' : 'inactive'}">
                        ${level.isActive ? 'Active' : 'Inactive'}
                    </span>
                    <span class="questions-count">${level.questions.length} Questions</span>
                </div>
            </div>
            <div class="level-actions" onclick="event.stopPropagation()">
                <button class="btn-primary" onclick="showAddQuestionModal(${level.levelID})">
                    <i class="fas fa-plus"></i> Add Question
                </button>
                <!--<button class="btn-secondary" onclick="editLevel(${level.levelID})">
                    <i class="fas fa-edit"></i> Edit Level
                </button>
                <button class="btn-toggle ${level.isActive ? 'btn-warning' : 'btn-success'}" onclick="toggleLevel(${level.levelID})">
                    <i class="fas fa-power-off"></i> ${level.isActive ? 'Deactivate' : 'Activate'}
                </button>
                -->
            </div>
        </div>
        <div class="level-questions collapsed ${level.questions.length === 0 ? 'empty' : ''}" id="level-questions-${level.levelID}">
            ${level.questions.length === 0 ?
                '<div class="empty-questions">No questions added yet. Click "Add Question" to get started.</div>' :
                questionsHtml
            }
        </div>
    `;

    return card;
}

function showAddQuestionModal(levelID) {
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('questionForm').reset();
    document.getElementById('levelNumber').value = levelID;
    document.getElementById('contentId').value = '';
    document.getElementById('questionForm').onsubmit = (e) => handleAddQuestion(e, levelID);
    document.getElementById('questionModal').classList.add('show');
}

function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const question = level.questions.find(q => q.id === questionId);
    if (!question) return;

    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('questionText').value = question.question;
    document.getElementById('option1').value = question.options[0];
    document.getElementById('option2').value = question.options[1];
    document.getElementById('option3').value = question.options[2];
    document.getElementById('option4').value = question.options[3];
    document.getElementById('correctAnswer').value = question.correctAnswer;
    document.getElementById('levelNumber').value = levelID;
    document.getElementById('contentId').value = questionId;

    document.getElementById('questionForm').onsubmit = (e) => handleEditQuestion(e, questionId, levelID);
    document.getElementById('questionModal').classList.add('show');
}

async function handleAddQuestion(event, levelID) {
    event.preventDefault();

    const questionData = {
        level_number: parseInt(document.getElementById('levelNumber').value) || levelID,
        question_text: document.getElementById('questionText').value,
        option1: document.getElementById('option1').value,
        option2: document.getElementById('option2').value,
        option3: document.getElementById('option3').value,
        option4: document.getElementById('option4').value,
        correct_answer: parseInt(document.getElementById('correctAnswer').value)
    };

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(questionData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Question added successfully', 'success');
            await loadGameContent();
            displayGameLevels();
            closeModal('questionModal');
        } else {
            showNotification(result.error || 'Failed to add question', 'error');
        }
    } catch (error) {
        console.error('Error adding question:', error);
        showNotification('Error adding question', 'error');
    }
}

async function handleEditQuestion(event, questionId, levelID) {
    event.preventDefault();

    const questionData = {
        content_id: questionId,
        level_number: parseInt(document.getElementById('levelNumber').value) || levelID,
        question_text: document.getElementById('questionText').value,
        option1: document.getElementById('option1').value,
        option2: document.getElementById('option2').value,
        option3: document.getElementById('option3').value,
        option4: document.getElementById('option4').value,
        correct_answer: parseInt(document.getElementById('correctAnswer').value)
    };

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=update_content`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(questionData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Question updated successfully', 'success');
            await loadGameContent();
            displayGameLevels();
            closeModal('questionModal');
        } else {
            showNotification(result.error || 'Failed to update question', 'error');
        }
    } catch (error) {
        console.error('Error updating question:', error);
        showNotification('Error updating question', 'error');
    }
}

async function deleteQuestion(questionId, levelID) {
    if (confirm('Are you sure you want to delete this question?')) {
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=delete_content&content_id=${questionId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Question deleted successfully', 'success');
                await loadGameContent();
                displayGameLevels();
            } else {
                showNotification(result.error || 'Failed to delete question', 'error');
            }
        } catch (error) {
            console.error('Error deleting question:', error);
            showNotification('Error deleting question', 'error');
        }
    }
}

// Level management functions
function toggleLevelDropdown(levelID) {
    const questionsContainer = document.getElementById(`level-questions-${levelID}`);
    const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

    if (!questionsContainer || !dropdownToggle) return;

    const isCollapsed = questionsContainer.classList.contains('collapsed');

    if (isCollapsed) {
        // Expand
        questionsContainer.classList.remove('collapsed');
        questionsContainer.classList.add('expanded');
        dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
    } else {
        // Collapse
        questionsContainer.classList.remove('expanded');
        questionsContainer.classList.add('collapsed');
        dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
    }
}

function editLevel(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const newName = prompt('Enter new level name:', level.levelName);
    if (newName && newName.trim() !== '') {
        level.levelName = newName.trim();
        displayGameLevels();
    }
}

function toggleLevel(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const action = level.isActive ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} this level?`)) {
        level.isActive = !level.isActive;
        displayGameLevels();
    }
}

function toggleAllLevels() {
    const toggleButton = document.getElementById('toggleAllLevels');
    const isExpanding = toggleButton.textContent.includes('Expand');

    gameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${level.levelID}`);

        if (questionsContainer && dropdownToggle) {
            if (isExpanding) {
                // Expand all
                questionsContainer.classList.remove('collapsed');
                questionsContainer.classList.add('expanded');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
            } else {
                // Collapse all
                questionsContainer.classList.remove('expanded');
                questionsContainer.classList.add('collapsed');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
            }
        }
    });

    // Update button text and icon
    if (isExpanding) {
        toggleButton.innerHTML = '<i class="fas fa-compress-alt"></i> Collapse All';
    } else {
        toggleButton.innerHTML = '<i class="fas fa-expand-alt"></i> Expand All';
    }
}

// Modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Event listeners
document.addEventListener('DOMContentLoaded', async function() {
    await initializeData();
    displayUsers();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.user-actions')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Close modals when clicking outside
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.remove('show');
            }
        });
    });
});

