<?php
session_start();
// require_once 'dbconnection.php';

if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}
$pdo = new PDO('mysql:host=localhost;dbname=dbfunconnect', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Start transaction
$pdo->beginTransaction();

$userId = $_SESSION['userId'];
$stmt = $pdo->prepare("SELECT userExp FROM user_tier WHERE user_id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if ($user) {
    echo json_encode(['success' => true, 'userExp' => $user['userExp']]);
} else {
    echo json_encode(['success' => false, 'message' => 'User not found']);
}
?>