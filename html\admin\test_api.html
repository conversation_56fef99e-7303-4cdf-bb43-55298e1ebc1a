<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Admin API Test Page</h1>
    
    <div class="test-section">
        <h2>Database Setup</h2>
        <button onclick="setupDatabase()">Setup Database</button>
        <div id="setupResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Game Content API Tests</h2>
        <button onclick="testGetAllContent()">Get All Content</button>
        <button onclick="testGetLevels()">Get Levels</button>
        <button onclick="testAddContent()">Add Test Content</button>
        <div id="contentResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Users API Tests</h2>
        <button onclick="testGetAllUsers()">Get All Users</button>
        <button onclick="testSearchUsers()">Search Users</button>
        <div id="usersResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = '../../php/admin/';

        async function setupDatabase() {
            try {
                const response = await fetch(`${API_BASE_URL}setup_database.php`);
                const result = await response.json();
                showResult('setupResult', result);
            } catch (error) {
                showResult('setupResult', { error: error.message });
            }
        }

        async function testGetAllContent() {
            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
                const result = await response.json();
                showResult('contentResult', result);
            } catch (error) {
                showResult('contentResult', { error: error.message });
            }
        }

        async function testGetLevels() {
            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_levels`);
                const result = await response.json();
                showResult('contentResult', result);
            } catch (error) {
                showResult('contentResult', { error: error.message });
            }
        }

        async function testAddContent() {
            const testData = {
                level_number: 1,
                question_text: "Test question from API test?",
                option1: "Option A",
                option2: "Option B",
                option3: "Option C",
                option4: "Option D",
                correct_answer: 2
            };

            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                const result = await response.json();
                showResult('contentResult', result);
            } catch (error) {
                showResult('contentResult', { error: error.message });
            }
        }

        async function testGetAllUsers() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
                const result = await response.json();
                showResult('usersResult', result);
            } catch (error) {
                showResult('usersResult', { error: error.message });
            }
        }

        async function testSearchUsers() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=search_users&search=user`);
                const result = await response.json();
                showResult('usersResult', result);
            } catch (error) {
                showResult('usersResult', { error: error.message });
            }
        }

        function showResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = JSON.stringify(data, null, 2);
        }
    </script>
</body>
</html>
