<?php
$pdo = new PDO('mysql:host=localhost;dbname=dbfunconnect', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$stmt = $pdo->query("SELECT expID, expName FROM exp ORDER BY expID");
$tiers = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($tiers) {
    echo json_encode(['success' => true, 'tier' => $tiers]);
} else {
    echo json_encode(['success' => false, 'message' => 'No tiers found']);
}
